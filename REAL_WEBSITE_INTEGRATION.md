# 真实网站集成实现

## 功能概述
在模拟的Chrome窗口中嵌入真实的京东网站，实现真正的网站浏览和搜索功能，而不是模拟内容。

## 技术实现

### 1. iframe集成 🌐
```typescript
<iframe
  src={
    currentStep === 'search-results' 
      ? 'https://search.jd.com/Search?keyword=Mac%E7%94%B5%E8%84%91&enc=utf-8'
      : 'https://www.jd.com'
  }
  className="w-full h-full border-0"
  title="京东网站"
  sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
  loading="lazy"
/>
```

### 2. 动态URL切换 🔄
根据操作步骤显示不同的页面：

#### 初始状态 (jd-loaded, clicking-search, typing-search)
- **URL**: `https://www.jd.com`
- **显示**: 京东首页
- **功能**: 用户可以看到真实的京东首页内容

#### 搜索结果状态 (search-results)
- **URL**: `https://search.jd.com/Search?keyword=Mac%E7%94%B5%E8%84%91&enc=utf-8`
- **显示**: Mac电脑搜索结果页面
- **功能**: 显示真实的Mac电脑商品搜索结果

### 3. 安全配置 🔒
使用sandbox属性确保安全性：
```typescript
sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox"
```

**权限说明**:
- `allow-scripts`: 允许JavaScript执行
- `allow-same-origin`: 允许同源请求
- `allow-forms`: 允许表单提交
- `allow-popups`: 允许弹窗
- `allow-popups-to-escape-sandbox`: 允许弹窗逃离沙箱

## 操作流程

### 1. Chrome启动 🚀
```
用户操作: Left Click @ Chrome
模拟效果: Chrome图标高亮 → Chrome窗口出现
网站状态: 准备加载
```

### 2. 地址栏操作 📍
```
用户操作: Left Click @ address-bar
模拟效果: 地址栏获得焦点，显示输入状态
网站状态: 准备输入URL
```

### 3. URL输入 ⌨️
```
用户操作: Type @ jd.com
模拟效果: 地址栏逐字显示"jd.com"
网站状态: 显示输入进度
```

### 4. 页面加载 🌐
```
用户操作: Press @ Enter
模拟效果: 显示加载动画 → 京东首页iframe加载
网站状态: 显示真实的京东首页 (https://www.jd.com)
```

### 5. 搜索操作 🔍
```
用户操作: Left Click @ search-input
模拟效果: 搜索框获得焦点状态
网站状态: 京东首页保持显示
```

### 6. 搜索输入 📝
```
用户操作: Type @ Mac电脑
模拟效果: 逐字显示搜索词输入
网站状态: 京东首页保持显示
```

### 7. 搜索执行 🎯
```
用户操作: Left Click @ search-button
模拟效果: URL切换到搜索结果页面
网站状态: 显示真实的Mac电脑搜索结果
```

## 用户体验

### 1. 真实性 ✅
- 用户看到的是真实的京东网站
- 可以进行真实的浏览和交互
- 搜索结果是实时的真实数据

### 2. 同步性 ⚡
- 操作记录与网站状态完全同步
- 地址栏显示与iframe内容匹配
- 模拟操作与真实页面切换协调

### 3. 交互性 🎮
- 用户可以在iframe中进行真实操作
- 支持滚动、点击、表单填写等
- 完整的网站功能可用

## 技术优势

### 1. 真实数据 📊
- 显示真实的商品信息
- 实时的价格和库存
- 真实的用户评价和图片

### 2. 完整功能 🔧
- 支持所有京东网站功能
- 用户可以进行真实购物
- 完整的搜索和筛选功能

### 3. 无需维护 🛠️
- 不需要模拟数据维护
- 自动获取最新内容
- 无需更新商品信息

## 潜在限制

### 1. 跨域限制 🚫
某些网站可能不允许iframe嵌入：
- 设置了X-Frame-Options
- 使用了Content Security Policy
- 京东网站目前允许iframe嵌入

### 2. 性能考虑 ⚡
- iframe加载需要时间
- 网络依赖性较强
- 可能影响整体页面性能

### 3. 安全考虑 🔒
- 需要合适的sandbox配置
- 避免恶意脚本执行
- 保护用户隐私

## 扩展可能性

### 1. 多网站支持 🌐
```typescript
const getWebsiteUrl = (site: string, step: string) => {
  switch(site) {
    case 'jd':
      return step === 'search-results' 
        ? 'https://search.jd.com/Search?keyword=Mac%E7%94%B5%E8%84%91'
        : 'https://www.jd.com';
    case 'taobao':
      return step === 'search-results'
        ? 'https://s.taobao.com/search?q=Mac电脑'
        : 'https://www.taobao.com';
    default:
      return 'https://www.jd.com';
  }
};
```

### 2. 搜索词自定义 🔍
```typescript
const searchKeyword = encodeURIComponent(typedSearch || 'Mac电脑');
const searchUrl = `https://search.jd.com/Search?keyword=${searchKeyword}&enc=utf-8`;
```

### 3. 页面状态检测 📡
```typescript
// 监听iframe加载状态
const handleIframeLoad = () => {
  console.log('网站加载完成');
  // 可以添加加载完成的回调
};

<iframe
  onLoad={handleIframeLoad}
  // ... 其他属性
/>
```

## 调试和监控

### 1. 加载状态 📊
- 监控iframe加载时间
- 检测网络连接状态
- 处理加载失败情况

### 2. 错误处理 🛠️
```typescript
const handleIframeError = () => {
  console.error('网站加载失败');
  // 显示错误提示或备用内容
};
```

### 3. 性能优化 ⚡
- 使用loading="lazy"延迟加载
- 预加载关键页面
- 缓存常用URL

## 总结

通过iframe集成真实的京东网站，实现了：

- ✅ **真实性**: 显示真实的网站内容和数据
- ✅ **同步性**: 操作记录与网站状态完美同步
- ✅ **交互性**: 用户可以进行真实的网站操作
- ✅ **扩展性**: 可以轻松支持其他网站
- ✅ **维护性**: 无需维护模拟数据

这种实现方式大大提升了演示的真实性和说服力，用户可以看到真正的网站操作效果，而不是模拟的内容。
