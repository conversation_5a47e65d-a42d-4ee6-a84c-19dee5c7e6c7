import express from 'express';
import { createProxyMiddleware } from 'http-proxy-middleware';
import cors from 'cors';

const app = express();
const PORT = 3001;

// 启用CORS
app.use(cors({
  origin: 'http://localhost:5173', // Vite开发服务器地址
  credentials: true
}));

// 代理中间件配置
const proxyOptions = {
  target: 'https://search.jd.com',
  changeOrigin: true,
  secure: true,
  followRedirects: true,
  headers: {
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Referer': 'https://www.jd.com'
  },
  onProxyReq: (proxyReq, req, res) => {
    console.log('代理请求:', req.url);
  },
  onProxyRes: (proxyRes, req, res) => {
    // 移除X-Frame-Options头，允许在iframe中显示
    delete proxyRes.headers['x-frame-options'];
    delete proxyRes.headers['content-security-policy'];
    
    // 设置允许iframe嵌入的头
    proxyRes.headers['X-Frame-Options'] = 'ALLOWALL';
    
    console.log('代理响应:', proxyRes.statusCode);
  },
  onError: (err, req, res) => {
    console.error('代理错误:', err);
    res.status(500).send('代理服务器错误');
  }
};

// 设置代理路由
app.use('/api/proxy', (req, res, next) => {
  const targetUrl = req.query.url;
  if (!targetUrl) {
    return res.status(400).send('缺少目标URL参数');
  }
  
  // 动态设置代理目标
  const proxy = createProxyMiddleware({
    ...proxyOptions,
    target: targetUrl,
    pathRewrite: {
      '^/api/proxy': ''
    }
  });
  
  proxy(req, res, next);
});

app.listen(PORT, () => {
  console.log(`代理服务器运行在 http://localhost:${PORT}`);
  console.log('用于绕过京东网站的跨域限制');
});
