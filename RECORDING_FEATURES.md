# 录制模式操作记录功能

## 功能概述

在录制模式下，新增了一个半透明的操作记录窗口，实时显示用户在录制过程中的操作行为。这个功能模拟了真实的用户操作录制场景，为用户提供直观的操作反馈。

## 新增组件和功能

### 1. ActionLog 组件 (`src/components/ActionLog.tsx`)

**功能特性：**
- 半透明黑色背景，具有毛玻璃效果
- 固定定位在录制窗口下方 (`top-20 right-4`)
- 实时显示操作列表，支持自动滚动到最新操作
- 每个操作都有对应的图标、颜色和时间戳
- 支持最大高度限制和滚动条

**UI设计：**
- 宽度：320px (`w-80`)
- 最大高度：384px (`max-h-96`)
- 背景：`bg-black/20` 带有 `backdrop-blur-md`
- 边框：`border border-white/10`
- 自定义滚动条样式

### 2. 操作类型定义 (`src/types/recording.ts`)

**支持的操作类型：**
- `Left Click` - 左键点击 (蓝色，👆图标)
- `Right Click` - 右键点击 (紫色，👉图标)
- `Type` - 键盘输入 (绿色，⌨️图标)
- `Scroll` - 滚动操作 (橙色，📜图标)
- `Press` - 按键操作 (红色，🔘图标)

**数据结构：**
```typescript
interface RecordingAction {
  id: string;
  type: ActionType;
  target: string;
  details?: string;
  timestamp: number;
}
```

### 3. useRecordingActions Hook (`src/hooks/useRecordingActions.ts`)

**功能：**
- 管理录制操作的状态
- 在录制开始时自动生成模拟操作
- 支持清除操作记录
- 支持手动添加操作

**特性：**
- 自动模拟操作：录制开始后每1-3秒随机添加一个操作
- 使用测试数据模拟真实的用户操作场景
- 录制停止时自动清理定时器

## 测试数据场景

模拟用户在Chrome浏览器中搜索"Mac电脑"的完整操作流程：

1. **Left Click @ Chrome** - 打开Chrome浏览器
2. **Left Click @ `<class_id="address-bar">`** - 点击地址栏
3. **Type @ jd.com** - 输入京东网址
4. **Press @ Enter** - 按回车键访问
5. **Left Click @ `<class_id="search-input">`** - 点击搜索框
6. **Type @ Mac电脑** - 输入搜索关键词
7. **Left Click @ `<class_id="search-button">`** - 点击搜索按钮
8. **Scroll @ Page (Down 3 units)** - 向下滚动页面
9. **Left Click @ `<class_id="product-item-1">`** - 点击商品
10. **Right Click @ `<class_id="product-image">`** - 右键点击商品图片

## 使用方式

### 启动录制
1. 点击摄像头图标进入录制模式
2. 主窗口自动缩小并移动到右上角
3. 操作记录窗口自动出现在主窗口下方
4. 开始自动生成模拟操作

### 录制控制
- **暂停/继续**：点击暂停按钮可以暂停操作生成
- **停止录制**：点击停止按钮结束录制并清除所有操作记录

### 操作记录显示
- 每个操作显示操作类型、目标、序号和时间戳
- 最新操作会高亮显示（白色边框）
- 支持滚动查看历史操作
- 底部显示录制状态

## 技术实现

### 定位策略
- 录制窗口：`fixed top-4 right-4` (贴右上角)
- 操作记录窗口：`fixed top-20 right-4` (在录制窗口下方)

### 动画效果
- 窗口出现/消失：`opacity` 和 `translate-y` 过渡
- 操作项悬停：`hover:bg-white/10`
- 最新操作高亮：`ring-1 ring-white/20`

### 响应式设计
- 固定宽度确保在不同屏幕尺寸下的一致性
- 最大高度限制防止窗口过高
- 自动滚动确保最新操作可见

## 样式定制

### 颜色主题
- 背景：半透明黑色 (`bg-black/20`)
- 文字：白色系 (`text-white/90`, `text-white/80`, `text-white/60`)
- 边框：半透明白色 (`border-white/10`)
- 操作类型：彩色标识（蓝、紫、绿、橙、红）

### 毛玻璃效果
```css
backdrop-filter: blur(12px);
-webkit-backdrop-filter: blur(12px);
```

### 自定义滚动条
- 细滚动条：`scrollbar-thin`
- 半透明滑块：`scrollbar-thumb-white/20`
- 透明轨道：`scrollbar-track-transparent`

## 扩展性

### 添加新操作类型
1. 在 `ActionType` 中添加新类型
2. 在 `getActionColor` 和 `getActionIcon` 中添加对应的颜色和图标
3. 在 `formatAction` 中添加格式化逻辑

### 自定义操作生成
可以通过 `addAction` 方法手动添加操作：
```typescript
const { addAction } = useRecordingActions(isRecording);

addAction({
  type: 'Left Click',
  target: 'Custom Button',
  details: 'Additional info'
});
```

### 数据持久化
可以扩展 hook 来支持操作记录的保存和加载：
- 本地存储
- 服务器同步
- 导出为文件

## 性能优化

1. **虚拟滚动**：对于大量操作记录，可以实现虚拟滚动
2. **操作去重**：避免重复的操作记录
3. **内存管理**：定期清理过期的操作记录
4. **懒加载**：按需加载操作详情

## 未来改进

1. **真实操作捕获**：集成真实的鼠标和键盘事件监听
2. **操作回放**：支持操作记录的回放功能
3. **操作编辑**：允许用户编辑和删除特定操作
4. **导出功能**：支持导出操作记录为各种格式
5. **操作分析**：提供操作统计和分析功能
