interface ActionPanelProps {
  onRun: () => void;
}

/**
 * 操作面板组件，包含提示文本和运行按钮
 */
export const ActionPanel = ({ onRun }: ActionPanelProps) => {
  return (
    <div className="flex justify-between items-center">
      {/* 提示文本 */}
      <div
        className="text-gray-400 font-light text-xs"
        style={{
          fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
          fontWeight: '300',
          lineHeight: '14px',
          marginLeft: '0'
        }}
      >
        Press ESC to close
      </div>

      {/* 运行按钮 */}
      <button
        onClick={onRun}
        className={`
          flex items-center space-x-2 px-4 py-2 rounded-lg
          transition-all duration-300 ease-out
          hover:bg-purple-50/50 active:translate-y-px
          focus:outline-none focus:ring-2 focus:ring-purple-500/20
        `}
        style={{
          minWidth: '56px',
          minHeight: '40px'
        }}
      >
        {/* Dog icon */}
        <div className="relative">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#8E44AD"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="transition-transform duration-300 hover:scale-105 active:scale-95"
          >
            {/* Abstract running dog outline */}
            <path d="M7 15c0-1.5 1-2.5 2.5-2.5h5c1.5 0 2.5 1 2.5 2.5" />
            <path d="M5 13.5c-0.8 0-1.5-0.7-1.5-1.5s0.7-1.5 1.5-1.5" />
            <path d="M19 13.5c0.8 0 1.5-0.7 1.5-1.5s-0.7-1.5-1.5-1.5" />
            <path d="M9 10.5c0-1.5 1.5-3 3-3s3 1.5 3 3" />
            <path d="M10.5 7.5c0-0.8 0.7-1.5 1.5-1.5s1.5 0.7 1.5 1.5" />
            <path d="M7 17l-1.5 2.5M10.5 17v2.5M13.5 17v2.5M17 17l1.5 2.5" />
          </svg>
        </div>
        
        {/* Run text */}
        <span
          className="text-gray-900 font-medium text-base"
          style={{
            fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
            fontWeight: '500'
          }}
        >
          Run!
        </span>
      </button>
    </div>
  );
};
