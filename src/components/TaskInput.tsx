import { useState, useEffect, useRef } from 'react';
import { Camera, Mic } from 'lucide-react';

interface TaskInputProps {
  value: string;
  onChange: (value: string) => void;
  onCameraClick: () => void;
  onMicClick: () => void;
  placeholder?: string;
}

/**
 * 任务输入组件，包含输入框和功能图标
 */
export const TaskInput = ({ 
  value, 
  onChange, 
  onCameraClick, 
  onMicClick, 
  placeholder = "Input your task..." 
}: TaskInputProps) => {
  const [isFocused, setIsFocused] = useState(false);
  const [cursorVisible, setCursorVisible] = useState(true);
  const inputRef = useRef<HTMLInputElement>(null);

  // 光标闪烁动画
  useEffect(() => {
    if (!isFocused) return;
    
    const interval = setInterval(() => {
      setCursorVisible(prev => !prev);
    }, 800);
    
    return () => clearInterval(interval);
  }, [isFocused]);

  const handleInputFocus = () => {
    setIsFocused(true);
    setCursorVisible(true);
  };

  const handleInputBlur = () => {
    setIsFocused(false);
    setCursorVisible(false);
  };

  const handleIconClick = (iconType: 'camera' | 'microphone') => {
    if (iconType === 'camera') {
      onCameraClick();
    } else {
      onMicClick();
    }
  };

  return (
    <div className="relative">
      <div 
        className={`
          relative bg-white rounded-lg
          transition-all duration-200
          ${isFocused 
            ? 'ring-1 ring-purple-500/50 shadow-sm' 
            : 'ring-1 ring-gray-200/50'
          }
        `}
        style={{
          padding: '14px 18px',
          height: '52px',
          border: '1px solid rgba(245, 245, 247, 0.1)'
        }}
      >
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
          placeholder={placeholder}
          className={`
            w-full h-full bg-transparent border-none outline-none
            text-left resize-none
            ${isFocused 
              ? 'text-gray-900 font-medium text-base' 
              : 'text-gray-400 font-light text-sm'
            }
          `}
          style={{
            fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif',
            caretColor: 'transparent'
          }}
        />
        
        {/* 自定义光标 */}
        {isFocused && (
          <div 
            className={`
              absolute top-3.5 w-0.5 h-5 bg-purple-500
              transition-opacity duration-100
              ${cursorVisible ? 'opacity-100' : 'opacity-0'}
            `}
            style={{
              left: `${18 + (value.length * 8)}px`,
              animation: 'none'
            }}
          />
        )}
        
        {/* 功能图标 */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex space-x-2.5">
          <button
            onClick={() => handleIconClick('camera')}
            className="p-2 rounded-lg transition-all duration-200 hover:bg-purple-500/10 active:scale-95"
            style={{ width: '22px', height: '22px', padding: '0' }}
          >
            <Camera 
              size={22} 
              className="text-gray-400 hover:text-purple-500 transition-colors duration-200" 
            />
          </button>
          <button
            onClick={() => handleIconClick('microphone')}
            className="p-2 rounded-lg transition-all duration-200 hover:bg-purple-500/10 active:scale-95"
            style={{ width: '22px', height: '22px', padding: '0' }}
          >
            <Mic 
              size={22} 
              className="text-gray-400 hover:text-purple-500 transition-colors duration-200" 
            />
          </button>
        </div>
      </div>
    </div>
  );
};
