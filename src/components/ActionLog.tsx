import { useEffect, useRef } from 'react';
import { RecordingAction } from '../types/recording';
import chromeIcon from '../assets/chrome.svg';

interface ActionLogProps {
  actions: RecordingAction[];
  isVisible: boolean;
}

/**
 * 操作记录组件，显示录制时的用户操作
 */
export const ActionLog = ({ actions, isVisible }: ActionLogProps) => {
  const logRef = useRef<HTMLDivElement>(null);

  // 自动滚动到最新操作
  useEffect(() => {
    if (logRef.current && actions.length > 0) {
      logRef.current.scrollTop = logRef.current.scrollHeight;
    }
  }, [actions]);

  // 格式化操作文本 - 提取class_id内容
  const formatTarget = (target: string): string => {
    // 匹配 <class_id="..."> 格式并提取内容
    const classIdMatch = target.match(/<class_id="([^"]+)">/);
    if (classIdMatch) {
      return classIdMatch[1];
    }
    return target;
  };

  // 检查是否是Chrome相关操作
  const isChromeContext = (actions: RecordingAction[], currentIndex: number): boolean => {
    // 查找当前操作之前最近的Chrome操作
    for (let i = currentIndex; i >= 0; i--) {
      const action = actions[i];
      if (formatTarget(action.target).toLowerCase() === 'chrome') {
        return true;
      }
      // 如果遇到其他应用程序操作，则退出Chrome上下文
      if (action.type === 'Left Click' &&
          !formatTarget(action.target).toLowerCase().includes('chrome') &&
          !action.target.includes('class_id')) {
        return false;
      }
    }
    return false;
  };

  // 格式化操作信息
  const formatAction = (action: RecordingAction): { operation: string; target: string } => {
    const { type, target, details } = action;

    switch (type) {
      case 'Left Click':
        return { operation: 'Left Click', target: formatTarget(target) };
      case 'Right Click':
        return { operation: 'Right Click', target: formatTarget(target) };
      case 'Type':
        return { operation: 'Type', target: formatTarget(target) };
      case 'Scroll':
        return {
          operation: 'Scroll',
          target: `${formatTarget(target)}${details ? ` (${details})` : ''}`
        };
      case 'Press':
        return { operation: 'Press', target: formatTarget(target) };
      default:
        return { operation: type, target: formatTarget(target) };
    }
  };



  // Chrome图标组件
  const ChromeIcon = ({ size = 12 }: { size?: number }) => (
    <img
      src={chromeIcon}
      alt="Chrome"
      width={size}
      height={size}
      className="flex-shrink-0"
    />
  );

  if (!isVisible || actions.length === 0) {
    return null;
  }

  return (
    <div
      className={`
        fixed top-20 right-4 z-40
        w-80 max-h-96
        bg-white/10 backdrop-blur-xl
        rounded-xl border border-white/20 shadow-2xl
        transition-all duration-300 ease-out
        ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}
      `}
      style={{
        backdropFilter: 'blur(20px)',
        WebkitBackdropFilter: 'blur(20px)',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
      }}
    >
      {/* 标题栏 */}
      <div className="px-4 py-3 border-b border-white/20">
        <h3 className="text-gray-800 text-sm font-semibold drop-shadow-sm">
          Recording Actions ({actions.length})
        </h3>
      </div>

      {/* 操作列表 */}
      <div
        ref={logRef}
        className="max-h-80 overflow-y-auto scrollbar-hide"
        style={{
          scrollbarWidth: 'none', /* Firefox */
          msOverflowStyle: 'none', /* IE and Edge */
        }}
      >
        <div className="p-2 space-y-1">
          {actions.map((action, index) => {
            const isInChromeContext = isChromeContext(actions, index);
            const isCurrentChrome = formatTarget(action.target).toLowerCase() === 'chrome';
            const { operation, target } = formatAction(action);

            return (
              <div key={action.id} className="relative">
                <div
                  className={`
                    flex items-center space-x-2 p-2 rounded-lg h-12 relative
                    bg-white/10 hover:bg-white/20
                    transition-colors duration-200 backdrop-blur-sm
                    ${index === actions.length - 1 ? 'ring-1 ring-gray-400/50 bg-white/20' : ''}
                    ${isInChromeContext && !isCurrentChrome ? 'ml-3' : ''}
                  `}
                >
                  {/* Chrome图标指示器 */}
                  {isCurrentChrome && (
                    <div className="flex-shrink-0">
                      <ChromeIcon size={20} />
                    </div>
                  )}

                  {/* 操作内容 */}
                  <div className="flex-1 min-w-0 flex items-center">
                    <div className="w-full truncate">
                      <span className="text-xs drop-shadow-sm">
                        <span className="font-semibold text-blue-600">
                          {operation}
                        </span>
                        <span className="mx-1 text-gray-500 font-normal">
                           
                        </span>
                        <span className="font-medium text-gray-800">
                          {target}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 底部状态 */}
      <div className="px-4 py-2 border-t border-white/20">
        <div className="text-gray-600 text-xs text-center font-medium drop-shadow-sm">
          Live recording in progress...
        </div>
      </div>
    </div>
  );
};
