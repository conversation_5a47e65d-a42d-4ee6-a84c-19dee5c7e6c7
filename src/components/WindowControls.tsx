import { useState } from 'react';

interface WindowControlsProps {
  onClose?: () => void;
  onMinimize?: () => void;
  onMaximize?: () => void;
}

/**
 * Mac风格的窗口控制按钮组件
 */
export const WindowControls = ({ onClose, onMinimize, onMaximize }: WindowControlsProps) => {
  const [isHovering, setIsHovering] = useState(false);

  const handleControlClick = (action: string, callback?: () => void) => {
    console.log(`Window ${action} clicked`);
    if (callback) {
      callback();
    }
  };

  return (
    <div 
      className="absolute top-5 left-5 flex space-x-2"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      {/* Close button */}
      <button
        onClick={() => handleControlClick('close', onClose)}
        className="w-3 h-3 rounded-full bg-red-500 hover:bg-red-600 transition-all duration-200 group relative"
      >
        {isHovering && (
          <div className="absolute inset-0 flex items-center justify-center">
            <svg width="6" height="6" viewBox="0 0 6 6" className="text-red-800 opacity-60">
              <path d="M1 1l4 4M5 1L1 5" stroke="currentColor" strokeWidth="1" strokeLinecap="round"/>
            </svg>
          </div>
        )}
      </button>
      
      {/* Minimize button */}
      <button
        onClick={() => handleControlClick('minimize', onMinimize)}
        className="w-3 h-3 rounded-full bg-yellow-500 hover:bg-yellow-600 transition-all duration-200 group relative"
      >
        {isHovering && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-2 h-0.5 bg-yellow-800 opacity-60 rounded-full"></div>
          </div>
        )}
      </button>
      
      {/* Maximize button */}
      <button
        onClick={() => handleControlClick('maximize', onMaximize)}
        className="w-3 h-3 rounded-full bg-green-500 hover:bg-green-600 transition-all duration-200 group relative"
      >
        {isHovering && (
          <div className="absolute inset-0 flex items-center justify-center">
            <svg width="4" height="4" viewBox="0 0 4 4" className="text-green-800 opacity-60">
              <path d="M0.5 1.5L2 0L3.5 1.5L2 3L0.5 1.5Z" fill="none" stroke="currentColor" strokeWidth="0.5"/>
              <path d="M2 0.5L2 2.5" stroke="currentColor" strokeWidth="0.5"/>
            </svg>
          </div>
        )}
      </button>
    </div>
  );
};
