import { ReactNode } from 'react';
import { WindowControls } from './WindowControls';
import backgroundImage from '../assets/background.jpg';

interface FloatingWindowProps {
  isVisible: boolean;
  isRecordingMode: boolean;
  onClose?: () => void;
  onMinimize?: () => void;
  onMaximize?: () => void;
  children: ReactNode;
}

/**
 * 主浮动窗口组件
 */
export const FloatingWindow = ({
  isVisible,
  isRecordingMode,
  onClose,
  onMinimize,
  onMaximize,
  children
}: FloatingWindowProps) => {
  if (isRecordingMode) {
    // 录制模式：固定在右上角
    return (
      <div
        className={`
          fixed top-4 right-4 z-50
          w-40 h-12 bg-white/85 backdrop-blur-xl
          rounded-2xl shadow-lg
          transition-all duration-500 ease-out
          ${isVisible
            ? 'opacity-100 translate-y-0 scale-100'
            : 'opacity-0 translate-y-8 scale-95'
          }
        `}
        style={{
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)',
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
        }}
      >
        <div className="p-2 h-full flex items-center justify-center">
          {children}
        </div>
      </div>
    );
  }

  // 正常模式：居中显示
  return (
    <div
      className="min-h-screen flex items-center justify-center p-6"
      style={{
        backgroundImage: `url(${backgroundImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }}
    >
      {/* Main floating window */}
      <div
        className={`
          relative bg-white/85 backdrop-blur-xl
          rounded-2xl shadow-lg
          transition-all duration-500 ease-out
          w-full max-w-2xl
          ${isVisible
            ? 'opacity-100 translate-y-0 scale-100'
            : 'opacity-0 translate-y-8 scale-95'
          }
        `}
        style={{
          boxShadow: '0 4px 16px rgba(0, 0, 0, 0.12)',
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
        }}
      >
        {/* Mac window controls */}
        <WindowControls
          onClose={onClose}
          onMinimize={onMinimize}
          onMaximize={onMaximize}
        />

        {/* Content area */}
        <div className="pt-12 pb-6 px-6 space-y-5">
          {children}
        </div>
      </div>
    </div>
  );
};
