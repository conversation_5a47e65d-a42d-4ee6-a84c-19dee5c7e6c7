/**
 * 录制操作类型
 */
export type ActionType = 'Left Click' | 'Right Click' | 'Type' | 'Scroll' | 'Press' | 'Copy';

/**
 * 录制操作数据结构
 */
export interface RecordingAction {
  id: string;
  type: ActionType;
  target: string;
  details?: string;
  timestamp: number;
}

/**
 * 生成测试数据：模拟用户在Chrome中搜索"Mac电脑"的操作
 */
export const generateTestActions = (): RecordingAction[] => {
  const baseTime = Date.now();
  
  return [
    {
      id: '1',
      type: 'Left Click',
      target: 'Chrome',
      timestamp: baseTime
    },
    {
      id: '2',
      type: 'Left Click',
      target: '<class_id="address-bar">',
      timestamp: baseTime + 1500
    },
    {
      id: '3',
      type: 'Type',
      target: 'search.jd.com',
      timestamp: baseTime + 2000
    },
    {
      id: '4',
      type: 'Press',
      target: 'Enter',
      timestamp: baseTime + 3500
    },
    {
      id: '5',
      type: 'Left Click',
      target: '<class_id="search-input">',
      timestamp: baseTime + 6000
    },
    {
      id: '6',
      type: 'Type',
      target: 'Mac电脑',
      timestamp: baseTime + 6500
    },
    {
      id: '7',
      type: 'Left Click',
      target: '<class_id="search-button">',
      timestamp: baseTime + 8000
    },
    {
      id: '8',
      type: 'Scroll',
      target: 'Page',
      details: 'Down 3 units',
      timestamp: baseTime + 10000
    },
    {
      id: '9',
      type: 'Left Click',
      target: '<class_id="GoodsContainer">',
      details: 'Click first product container',
      timestamp: baseTime + 12000
    },
    {
      id: '10',
      type: 'Copy',
      target: 'Product Title',
      details: 'Copy product title',
      timestamp: baseTime + 14000
    },
    {
      id: '11',
      type: 'Left Click',
      target: '<class_id="chrome-minimize-button">',
      details: 'Minimize Chrome window',
      timestamp: baseTime + 16000
    },
    {
      id: '12',
      type: 'Left Click',
      target: '<class_id="txt-file-icon">',
      details: 'Open text file',
      timestamp: baseTime + 18000
    },
    {
      id: '13',
      type: 'Left Click',
      target: '<class_id="txt-editor-content">',
      details: 'Click in text editor',
      timestamp: baseTime + 20000
    },
    {
      id: '14',
      type: 'Press',
      target: 'ctrl+v',
      details: 'Paste copied content',
      timestamp: baseTime + 22000
    },
    {
      id: '15',
      type: 'Left Click',
      target: '<class_id="stop-recording-button">',
      details: 'Stop recording',
      timestamp: baseTime + 24000
    }
  ];
};
