import { useState, useRef, useEffect } from 'react';

export interface UseRecordingReturn {
  isRecordingMode: boolean;
  isRecording: boolean;
  recordingTime: number;
  startRecording: () => void;
  stopRecording: () => void;
  togglePause: () => void;
}

/**
 * 录制功能的自定义hook
 */
export const useRecording = (): UseRecordingReturn => {
  const [isRecordingMode, setIsRecordingMode] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const recordingTimerRef = useRef<number | null>(null);

  // 清理计时器
  useEffect(() => {
    return () => {
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
      }
    };
  }, []);

  const startRecording = () => {
    setIsRecordingMode(true);
    setIsRecording(true);
    setRecordingTime(0);
    
    // 开始计时
    recordingTimerRef.current = setInterval(() => {
      setRecordingTime(prev => prev + 1);
    }, 1000);
  };

  const stopRecording = () => {
    setIsRecording(false);
    setIsRecordingMode(false);
    setRecordingTime(0);
    
    // 清除计时器
    if (recordingTimerRef.current) {
      clearInterval(recordingTimerRef.current);
      recordingTimerRef.current = null;
    }
  };

  const togglePause = () => {
    const newRecordingState = !isRecording;
    setIsRecording(newRecordingState);
    
    if (newRecordingState) {
      // 恢复计时
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } else {
      // 暂停计时
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
    }
  };

  return {
    isRecordingMode,
    isRecording,
    recordingTime,
    startRecording,
    stopRecording,
    togglePause,
  };
};
