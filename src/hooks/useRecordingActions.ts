import { useState, useEffect, useRef } from 'react';
import { RecordingAction, generateTestActions } from '../types/recording';

export interface UseRecordingActionsReturn {
  actions: RecordingAction[];
  clearActions: () => void;
  addAction: (action: Omit<RecordingAction, 'id' | 'timestamp'>) => void;
}

/**
 * 录制操作管理的自定义hook
 */
export const useRecordingActions = (isRecording: boolean): UseRecordingActionsReturn => {
  const [actions, setActions] = useState<RecordingAction[]>([]);
  const actionTimerRef = useRef<NodeJS.Timeout | null>(null);
  const testActionsRef = useRef<RecordingAction[]>([]);
  const currentIndexRef = useRef(0);

  // 初始化测试数据
  useEffect(() => {
    testActionsRef.current = generateTestActions();
  }, []);

  // 开始录制时启动模拟操作
  useEffect(() => {
    console.log('🎬 useRecordingActions effect:', { isRecording });

    if (isRecording) {
      console.log('▶️ Starting recording, resetting actions');
      // 重置状态
      currentIndexRef.current = 0;
      setActions([]);
      
      // 开始模拟操作
      const simulateActions = () => {
        if (currentIndexRef.current < testActionsRef.current.length) {
          const action = testActionsRef.current[currentIndexRef.current];

          console.log('➕ Adding action:', {
            index: currentIndexRef.current,
            action: { type: action.type, target: action.target }
          });

          // 添加操作到列表
          setActions(prev => {
            const newActions = [...prev, {
              ...action,
              timestamp: Date.now() // 使用当前时间戳
            }];
            console.log('📝 Actions updated, total:', newActions.length);
            return newActions;
          });

          currentIndexRef.current++;
          
          // 随机间隔1-3秒添加下一个操作
          const nextDelay = Math.random() * 2000 + 1000;
          actionTimerRef.current = setTimeout(simulateActions, nextDelay);
        }
      };
      
      // 延迟500ms开始第一个操作
      actionTimerRef.current = setTimeout(simulateActions, 500);
    } else {
      // 停止录制时清除定时器
      if (actionTimerRef.current) {
        clearTimeout(actionTimerRef.current);
        actionTimerRef.current = null;
      }
    }

    // 清理函数
    return () => {
      if (actionTimerRef.current) {
        clearTimeout(actionTimerRef.current);
        actionTimerRef.current = null;
      }
    };
  }, [isRecording]);

  const clearActions = () => {
    setActions([]);
    currentIndexRef.current = 0;
  };

  const addAction = (actionData: Omit<RecordingAction, 'id' | 'timestamp'>) => {
    const newAction: RecordingAction = {
      ...actionData,
      id: Date.now().toString(),
      timestamp: Date.now()
    };
    
    setActions(prev => [...prev, newAction]);
  };

  return {
    actions,
    clearActions,
    addAction
  };
};
