import { useState, useEffect } from 'react';

export interface UseWindowVisibilityReturn {
  isVisible: boolean;
  hideWindow: () => void;
  showWindow: () => void;
  toggleWindow: () => void;
}

/**
 * 窗口可见性管理的自定义hook
 */
export const useWindowVisibility = (): UseWindowVisibilityReturn => {
  const [isVisible, setIsVisible] = useState(false);

  // 窗口入场动画
  useEffect(() => {
    const timer = setTimeout(() => setIsVisible(true), 100);
    return () => clearTimeout(timer);
  }, []);

  // 处理ESC键关闭窗口
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        setIsVisible(false);
        setTimeout(() => setIsVisible(true), 300);
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  const hideWindow = () => {
    setIsVisible(false);
  };

  const showWindow = () => {
    setIsVisible(true);
  };

  const toggleWindow = () => {
    setIsVisible(prev => !prev);
  };

  return {
    isVisible,
    hideWindow,
    showWindow,
    toggleWindow,
  };
};
