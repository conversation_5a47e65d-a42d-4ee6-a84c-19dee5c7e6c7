# ACTOR-UI 项目重构说明

## 重构概述

本次重构将原本集中在 `App.tsx` 中的所有代码进行了合理的组件化拆分，提高了代码的可维护性、可复用性和可测试性。

## 新的项目结构

```
src/
├── components/           # 组件目录
│   ├── ActionPanel.tsx   # 操作面板组件
│   ├── FloatingWindow.tsx # 主浮动窗口组件
│   ├── RecordingPanel.tsx # 录制面板组件
│   ├── TaskInput.tsx     # 任务输入组件
│   ├── WindowControls.tsx # 窗口控制按钮组件
│   └── index.ts          # 组件导出文件
├── hooks/                # 自定义Hook目录
│   ├── useRecording.ts   # 录制功能Hook
│   ├── useWindowVisibility.ts # 窗口可见性Hook
│   └── index.ts          # Hook导出文件
├── utils/                # 工具函数目录
│   ├── time.ts           # 时间格式化工具
│   └── index.ts          # 工具函数导出文件
├── App.tsx               # 主应用组件（重构后）
├── main.tsx              # 应用入口
└── index.css             # 样式文件
```

## 组件说明

### 1. FloatingWindow 组件
- **职责**: 主浮动窗口容器，处理窗口的显示状态和布局
- **特性**: 
  - 支持录制模式和正常模式的不同布局
  - 集成窗口控制按钮
  - 响应式设计

### 2. WindowControls 组件
- **职责**: Mac风格的窗口控制按钮（关闭、最小化、最大化）
- **特性**: 
  - 悬停效果
  - 可配置的回调函数

### 3. TaskInput 组件
- **职责**: 任务输入框，包含自定义光标和功能图标
- **特性**: 
  - 自定义光标动画
  - 集成摄像头和麦克风图标
  - 焦点状态管理

### 4. RecordingPanel 组件
- **职责**: 录制模式下的控制面板
- **特性**: 
  - 实时显示录制时间
  - 停止和暂停/继续按钮
  - 紧凑的横向布局

### 5. ActionPanel 组件
- **职责**: 正常模式下的操作面板
- **特性**: 
  - 提示文本
  - 运行按钮

## 自定义Hook说明

### 1. useRecording Hook
- **职责**: 管理录制相关的状态和逻辑
- **功能**: 
  - 录制模式切换
  - 计时器管理
  - 暂停/继续功能
  - 自动清理资源

### 2. useWindowVisibility Hook
- **职责**: 管理窗口可见性和动画
- **功能**: 
  - 入场动画
  - ESC键关闭功能
  - 窗口显示/隐藏控制

## 工具函数说明

### time.ts
- **formatRecordingTime**: 将秒数格式化为 MM:SS 格式的时间字符串

## 重构优势

### 1. 代码组织
- **关注点分离**: 每个组件只负责特定的功能
- **模块化**: 便于独立开发和测试
- **可复用性**: 组件可以在其他项目中复用

### 2. 状态管理
- **集中管理**: 使用自定义Hook管理复杂状态逻辑
- **逻辑复用**: Hook可以在多个组件中复用
- **易于测试**: 状态逻辑与UI分离，便于单元测试

### 3. 维护性
- **清晰的文件结构**: 便于定位和修改代码
- **类型安全**: 完整的TypeScript类型定义
- **一致的接口**: 统一的组件Props接口

### 4. 扩展性
- **易于添加新功能**: 可以轻松添加新的组件或Hook
- **配置灵活**: 组件支持多种配置选项
- **主题支持**: 样式与逻辑分离，便于主题定制

## 使用示例

```tsx
import { useState } from 'react';
import { FloatingWindow, TaskInput, RecordingPanel, ActionPanel } from './components';
import { useRecording, useWindowVisibility } from './hooks';

function App() {
  const [inputValue, setInputValue] = useState('');
  const { isVisible, hideWindow, showWindow } = useWindowVisibility();
  const { 
    isRecordingMode, 
    isRecording, 
    recordingTime, 
    startRecording, 
    stopRecording, 
    togglePause 
  } = useRecording();

  return (
    <FloatingWindow
      isVisible={isVisible}
      isRecordingMode={isRecordingMode}
      onClose={() => {
        hideWindow();
        setTimeout(() => showWindow(), 500);
      }}
    >
      {isRecordingMode ? (
        <RecordingPanel
          recordingTime={recordingTime}
          isRecording={isRecording}
          onStop={stopRecording}
          onTogglePause={togglePause}
        />
      ) : (
        <>
          <TaskInput
            value={inputValue}
            onChange={setInputValue}
            onCameraClick={startRecording}
            onMicClick={() => console.log('Mic clicked')}
          />
          <ActionPanel onRun={() => console.log('Run clicked')} />
        </>
      )}
    </FloatingWindow>
  );
}
```

## 下一步优化建议

1. **添加单元测试**: 为每个组件和Hook编写测试用例
2. **性能优化**: 使用React.memo和useMemo优化渲染性能
3. **主题系统**: 实现可配置的主题系统
4. **国际化**: 添加多语言支持
5. **错误边界**: 添加错误处理和边界组件
6. **文档完善**: 为每个组件添加详细的API文档
